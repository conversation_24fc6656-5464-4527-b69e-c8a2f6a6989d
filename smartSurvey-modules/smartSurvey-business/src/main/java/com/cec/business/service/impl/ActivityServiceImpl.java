package com.cec.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.business.domain.bo.*;
import com.cec.business.domain.enums.*;
import com.cec.business.domain.req.ActivityFormQuery;
import com.cec.business.domain.req.ActivityReq;
import com.cec.business.domain.vo.ActivityApplyDetailVO;
import com.cec.business.domain.vo.ActivitySigninDetailVO;
import com.cec.business.domain.vo.ActivityVO;
import com.cec.business.mapper.*;
import com.cec.business.service.ActivityService;
import com.cec.business.utils.ShortIdUtils;
import com.cec.business.utils.UrlUtils;
import com.cec.common.core.constant.TenantConstants;
import com.cec.common.core.domain.dto.RoleDTO;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.ValidatorUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.SysUser;
import com.cec.system.domain.vo.SysDeptVo;
import com.cec.system.domain.vo.SysUserVo;
import com.cec.system.mapper.SysUserMapper;
import com.cec.system.service.ISysDeptService;
import com.cec.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class ActivityServiceImpl implements ActivityService {
    /**
     * 活动管理员角色键
     */
    private static final String ROLE_KEY_ACTIVITY_MANAGER = "activity_manager";
    /**
     * 活动成员角色键
     */
    private static final String ROLE_KEY_ACTIVITY_MEMBER = "activity_member";
    private final SysUserMapper sysUserMapper;
    private final ActivityMapper activityMapper;
    private final ActivityApplyMapper activityApplyMapper;
    private final ActivityRoleMapper activityRoleMapper;
    private final ActivityMemberRoleMapper activityMemberRoleMapper;
    private final UserFormAuthMapper userFormAuthMapper;
    private final ISysUserService sysUserService;
    private final ISysDeptService sysDeptService;
    private final ActivitySigninMapper activitySigninMapper;
    private final UserFormItemMapper userFormItemMapper;
    private final UrlUtils urlUtils;

    private static UpdateWrapper<ActivityEntity> buildUpdateWrapper(ActivityEntity update) {
        UpdateWrapper<ActivityEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", update.getId());
        try {
            PropertyDescriptor[] propertyDescriptors = java.beans.Introspector.getBeanInfo(ActivityEntity.class).getPropertyDescriptors();
            for (PropertyDescriptor pd : propertyDescriptors) {
                String propertyName = pd.getName();
                if ("class".equals(propertyName) || "id".equals(propertyName) || "params".equals(propertyName) || "searchValue".equals(propertyName)) {
                    continue;
                }
                Method readMethod = pd.getReadMethod();
                if (readMethod != null) {
                    Object value = readMethod.invoke(update);
                    String dbFieldName = camelToUnderline(propertyName);
                    updateWrapper.set(dbFieldName, value);
                }
            }
        } catch (Exception e) {
            log.error("更新活动主信息时反射获取属性失败", e);
            throw new ServiceException(MessageUtils.message("system.operation.failed"));
        }
        return updateWrapper;
    }

    /**
     * 驼峰命名转换为下划线命名
     *
     * @param camelCase 驼峰命名的字符串
     * @return 下划线命名的字符串
     */
    private static String camelToUnderline(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            if (camelCase.equals("deleted")) {
                return "is_deleted";
            } else {
                char c = camelCase.charAt(i);
                if (Character.isUpperCase(c)) {
                    if (i > 0) {
                        sb.append('_');
                    }
                    sb.append(Character.toLowerCase(c));
                } else {
                    sb.append(c);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 查询活动主
     *
     * @param id 主键
     * @return 活动主
     */
    @Override
    public ActivityVO queryById(Long id) {
        return activityMapper.selectVoById(id);
    }

    /**
     * 分页查询活动主列表
     *
     * @param param     查询条件
     * @param pageQuery 分页参数
     * @return 活动主分页列表
     */
    @Override
    public TableDataInfo<ActivityVO> queryPageList(ActivityFormQuery param, PageQuery pageQuery) {
        LambdaQueryWrapper<ActivityEntity> lqw = Wrappers.<ActivityEntity>lambdaQuery()
            .like(ObjectUtil.isNotEmpty(param.getName()), ActivityEntity::getName, param.getName())
            .eq(ObjectUtil.isNotNull(param.getStatus()), ActivityEntity::getStatus, param.getStatus())
            .orderByDesc(ActivityEntity::getCreateTime);
        addPermissionFilter(lqw);
        Page<ActivityVO> result = (Page<ActivityVO>) activityMapper.selectVoPage(pageQuery.build(), lqw)
            .convert(item -> {
                item.setCreateByName(getSystemUserName(item.getCreateBy()));
                item.setUpdateByName(getSystemUserName(item.getCreateBy()));
                item.setRoleKey(getActivityRoleKey(item.getId()));
                return item;
            });
        return TableDataInfo.build(result);
    }

    /**
     * 添加权限过滤条件
     *
     * @param lqw 查询条件构造器
     */
    private void addPermissionFilter(LambdaQueryWrapper<ActivityEntity> lqw) {
        // 获取当前登录用户
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException(MessageUtils.message("project.user.not.login"));
        }

        Long userId = loginUser.getUserId();

        // 超级管理员可查看所有项目
        if (LoginHelper.isSuperAdmin()) {
            return;
        }

        // 检查用户角色是否包含超级管理员或租户管理员角色
        if (loginUser.getRoles() != null) {
            boolean isSuperAdmin = loginUser.getRoles().stream()
                .anyMatch(role -> TenantConstants.SUPER_ADMIN_ROLE_KEY.equals(role.getRoleKey()));

            if (isSuperAdmin) {
                return;
            }
        }

        // 查询用户关联的项目ID列表（作为项目管理员或项目成员）
        // 使用子查询方式，减少多次数据库查询
        lqw.and(wrapper ->
            wrapper.inSql(ActivityEntity::getId,
                    "select distinct activity_id from activity_member_role where user_id = " + userId)
                .or()
                .inSql(ActivityEntity::getId,
                    "select distinct activity_id from activity_member_role where dept_id = " + loginUser.getDeptId())
        );
    }

    /**
     * 获取活动角色权限
     *
     * @param activityId 项目ID
     * @return 用户在项目中的角色键
     * @throws ServiceException 如果当前用户没有权限访问该项目
     */
    @Override
    public String getActivityRoleKey(Long activityId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException(MessageUtils.message("project.user.not.login.access"));
        }

        Long userId = loginUser.getUserId();

        // 1. 判断是否为超级管理员
        if (LoginHelper.isSuperAdmin()) {
            // 超级管理员，直接放行
            return TenantConstants.SUPER_ADMIN_ROLE_KEY;
        }

        // 检查用户角色是否包含超级管理员或租户管理员角色
        if (loginUser.getRoles() != null) {
            String superRole = loginUser.getRoles().stream()
                .map(RoleDTO::getRoleKey)
                .filter(TenantConstants.SUPER_ADMIN_ROLE_KEY::equals)
                .findFirst()
                .orElse(null);

            if (superRole != null) {
                return superRole;
            }
        }

        // 检查用户角色是否包含超级管理员或租户管理员角色
        if (loginUser.getRoles() != null) {
            String superRole = loginUser.getRoles().stream()
                .map(RoleDTO::getRoleKey)
                .filter(TenantConstants.SUPER_ADMIN_ROLE_KEY::equals)
                .findFirst()
                .orElse(null);

            if (superRole != null) {
                return superRole;
            }
        }

        // 2. 判断是否为项目管理员
        LambdaQueryWrapper<ActivityMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityMemberRole::getActivityId, activityId)
            .eq(ActivityMemberRole::getUserId, userId)
            .eq(ActivityMemberRole::getRoleKey, ROLE_KEY_ACTIVITY_MANAGER);

        if (activityMemberRoleMapper.exists(queryWrapper)) {
            // 项目管理员，有权限
            return ROLE_KEY_ACTIVITY_MANAGER;
        }

        // 3. 判断是否为项目成员
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityMemberRole::getActivityId, activityId)
            .eq(ActivityMemberRole::getUserId, userId)
            .eq(ActivityMemberRole::getRoleKey, ROLE_KEY_ACTIVITY_MEMBER);

        if (activityMemberRoleMapper.exists(queryWrapper)) {
            // 项目成员，有权限
            return ROLE_KEY_ACTIVITY_MEMBER;
        }

        // 4. 判断用户是否属于项目关联的部门
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityMemberRole::getActivityId, activityId)
            .isNotNull(ActivityMemberRole::getDeptId)
            .eq(ActivityMemberRole::getRoleKey, ROLE_KEY_ACTIVITY_MEMBER)
            .eq(ActivityMemberRole::getDeptId, loginUser.getDeptId());

        if (activityMemberRoleMapper.exists(queryWrapper)) {
            // 用户所在部门是项目成员部门，有权限
            return ROLE_KEY_ACTIVITY_MEMBER;
        }

        // 没有任何角色权限，抛出异常
        throw new ServiceException(MessageUtils.message("activity.no.permission.access"));
    }

    private String getSystemUserName(Long userId) {
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser == null) {
            return null;
        }
        return sysUser.getUserName();
    }

    /**
     * 查询符合条件的活动主列表
     *
     * @param param 查询条件
     * @return 活动主列表
     */
    @Override
    public List<ActivityVO> queryList(ActivityFormQuery param) {
        return activityMapper.selectVoList(Wrappers.<ActivityEntity>lambdaQuery()
            .like(ObjectUtil.isNotEmpty(param.getName()), ActivityEntity::getName, param.getName())
            .eq(ObjectUtil.isNotNull(param.getStatus()), ActivityEntity::getStatus, param.getStatus())
            .orderByDesc(ActivityEntity::getCreateTime));
    }

    /**
     * 新增活动主
     *
     * @param bo 活动主
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ActivityEntity bo) {
        ActivityEntity add = MapstructUtils.convert(bo, ActivityEntity.class);
        validEntityBeforeSave(add);
        return activityMapper.insert(add) > 0;
    }

    /**
     * 修改活动主
     *
     * @param form 活动主
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ActivityReq form) {
        ActivityEntity update = activityMapper.selectById(form.getId());
        BeanUtils.copyProperties(form, update, "id");
        validEntityBeforeSave(update);
        // 根据传过来的字段进行修改实体
        UpdateWrapper<ActivityEntity> updateWrapper = buildUpdateWrapper(update);
        boolean updateFlag = activityMapper.update(null, updateWrapper) > 0;
        String roleKey = getActivityRoleKey(form.getId());
        if (!ROLE_KEY_ACTIVITY_MEMBER.equals(roleKey)) {
            // 非项目成员的其他角色，可以修改活动的管理员、成员角色
            saveActivityRoles(form, form.getId());
        }
        // 新增表单Auth关联数据
        saveFormAuth(form);
        return updateFlag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ActivityEntity entity) {
        if (entity == null) {
            throw new ServiceException(MessageUtils.message("form.data.empty"));
        }
        if (entity.getSigninEnabled()) {
            entity.setSigninUrl(urlUtils.getSurveyActivityUrl(entity.getFormKey(), "sign"));
        } else {
            entity.setSigninUrl(null);
        }
        if (entity.getApplyEnabled()) {
            entity.setApplyUrl(urlUtils.getSurveyActivityUrl(entity.getFormKey(), "apply"));
        } else {
            entity.setApplyUrl(null);
        }
        if (entity.getFormEnabled()) {
            entity.setFormUrl(urlUtils.getSurveyH5Url(entity.getFormKey()));
        } else {
            entity.setFormUrl(null);
        }
    }

    /**
     * 校验并批量删除活动主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return activityMapper.deleteByIds(ids) > 0;
    }

    /***
     * 创建活动表单，并返回表单id
     * @param form  表单信息
     * @return 表单id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityVO createForm(ActivityReq form) {
        ValidatorUtils.validateEntity(form, AddGroup.class);
        fillActivityModel(form);
        ActivityEntity entity = BeanUtil.copyProperties(form, ActivityEntity.class);
        entity.setDeleted(Boolean.FALSE);
        activityMapper.insert(entity);
        // 设置活动的项目管理员、成员角色
        saveActivityRoles(form, entity.getId());
        // 新增表单Auth关联数据
        saveFormAuth(form);
        ActivityVO vo = BeanUtil.copyProperties(form, ActivityVO.class);
        vo.setId(entity.getId());
        vo.setCreated(true);
        return vo;
    }

    private void fillActivityModel(ActivityReq form) {
        form.setId(IdWorker.getId());
        form.setFormKey(ShortIdUtils.genId());
        if (ObjectUtil.isEmpty(form.getStatus())) {
            form.setStatus(ActivityStatusEnum.CREATE);
        } else {
            form.setStatus(form.getStatus());
        }
        if (form.getSigninEnabled()) {
            form.setSigninUrl(urlUtils.getSurveyActivityUrl(form.getFormKey(),"sign"));
        }
        if (form.getApplyEnabled()) {
            form.setApplyUrl(urlUtils.getSurveyActivityUrl(form.getFormKey(),"apply"));
        }
        if (form.getFormEnabled()) {
            form.setFormUrl(urlUtils.getSurveyH5Url(form.getFormKey()));
        }
    }



    private void saveActivityRoles(ActivityReq form, Long id) {
        if (ObjectUtil.isNull(id)) {
            return;
        }

        List<ActivityRole> activityRoles = getActivityRoles();
        Map<String, ActivityRole> roleMap = activityRoles
            .stream()
            .collect(Collectors.toMap(ActivityRole::getRoleKey, role -> role, (a, b) -> a));

        // 获取角色
        ActivityRole managerRole = roleMap.get(ROLE_KEY_ACTIVITY_MANAGER);
        ActivityRole memberRole = roleMap.get(ROLE_KEY_ACTIVITY_MEMBER);

        activityMemberRoleMapper.delete(new LambdaQueryWrapper<ActivityMemberRole>()
            .eq(ActivityMemberRole::getActivityId, id)
        );

        // 为项目分配各类角色
        assignActivityRoles(id, managerRole, form.getActivityManagerUserIds(), null, "管理员");
        assignActivityRoles(id, memberRole, form.getActivityMemberUserIds(), null, "成员");
        assignActivityRoles(id, memberRole, null, form.getActivityDeptIds(), "部门成员");
    }

    /***
     * 新增授权表单
     * @param form  表单信息
     */
    private void saveFormAuth(ActivityReq form) {
        // 先删除授权信息
        userFormAuthMapper.delete(Wrappers.<UserFormAuthEntity>lambdaQuery()
            .eq(UserFormAuthEntity::getFormKey, form.getFormKey()));

        // 再新增授权信息
        UserFormAuthEntity auth = new UserFormAuthEntity();
        auth.setFormKey(form.getFormKey());
        auth.setDeptIdList(form.getDeptIdList());
        auth.setUserIdList(form.getUserIdList());
        userFormAuthMapper.insert(auth);
    }

    /**
     * 分配项目角色
     *
     * @param activityId   项目ID
     * @param role         角色信息
     * @param userIds      用户ID数组
     * @param deptIds      部门ID数组
     * @param roleTypeName 角色类型名称(用于日志)
     */
    private void assignActivityRoles(Long activityId, ActivityRole role, Long[] userIds, Long[] deptIds, String roleTypeName) {
        // 检查角色是否存在
        if (role == null) {
            log.warn("项目[{}]分配{}角色失败: 角色不存在", activityId, roleTypeName);
            return;
        }

        // 分配用户角色
        if (userIds != null && userIds.length > 0) {
            log.info("为项目[{}]分配{}个{}", activityId, userIds.length, roleTypeName);
            List<ActivityMemberRole> memberRoles = Arrays.stream(userIds)
                .map(userId -> createMemberRole(activityId, role, userId, null))
                .toList();

            if (!memberRoles.isEmpty()) {
                activityMemberRoleMapper.insertBatch(memberRoles);
            }
        }

        // 分配部门角色
        if (deptIds != null && deptIds.length > 0) {
            log.info("为项目[{}]分配{}个{}部门", activityId, deptIds.length, roleTypeName);
            List<ActivityMemberRole> memberRoles = Arrays.stream(deptIds)
                .map(deptId -> createMemberRole(activityId, role, null, deptId))
                .toList();

            if (!memberRoles.isEmpty()) {
                activityMemberRoleMapper.insertBatch(memberRoles);
            }
        }
    }

    /**
     * 创建活动成员角色对象
     *
     * @param activityId 项目ID
     * @param role       角色信息
     * @param userId     用户ID
     * @param deptId     部门ID
     * @return 项目成员角色对象
     */
    private ActivityMemberRole createMemberRole(Long activityId, ActivityRole role, Long userId, Long deptId) {
        return new ActivityMemberRole()
            .setActivityId(activityId)
            .setRoleId(role.getRoleId())
            .setRoleName(role.getRoleName())
            .setRoleKey(role.getRoleKey())
            .setUserId(userId)
            .setDeptId(deptId);
    }

    /**
     * 获取活动角色列表
     *
     * @return 项目角色列表
     */
    private List<ActivityRole> getActivityRoles() {
        return activityRoleMapper.selectList();
    }

    @Override
    public ActivityEntity getByKey(String key) {
        return activityMapper.selectOne(Wrappers.<ActivityEntity>lambdaQuery()
            .eq(ActivityEntity::getFormKey, key));
    }

    /***
     * 查询当前用户的报名状态
     * @param entity    活动信息
     * @param detailVO  活动详情
     */
    private void getLoginUserApplyState(ActivityEntity entity, ActivityApplyDetailVO detailVO) {
        if (!entity.getApplyEnabled()) {
            detailVO.setApplyStatus(ApplyedStatusEnum.CLOSE);
            return;
        }
        ActivityApplyEntity apply = activityApplyMapper.selectOne(Wrappers.<ActivityApplyEntity>lambdaQuery()
            .eq(ActivityApplyEntity::getActivityId, entity.getId())
            .eq(ActivityApplyEntity::getUserId, LoginHelper.getUserId()));
        if (apply == null) {
            detailVO.setApplyStatus(ApplyedStatusEnum.CAN_APPLY);
            return;
        }
        if (apply.getAuditState() == ActivityApplyAuditStateEnum.AUDIT) {
            detailVO.setApplyedTime(apply.getCreateTime());
            detailVO.setApplyStatus(ApplyedStatusEnum.AUDITING);
        } else if (apply.getAuditState() == ActivityApplyAuditStateEnum.PASS || apply.getAuditState() == ActivityApplyAuditStateEnum.REJECT) {
            detailVO.setApplyedTime(apply.getCreateTime());
            detailVO.setApplyStatus(ApplyedStatusEnum.APPLYED);
        }
    }

//    private void checkActivityValidate(String url, ActivityEntity entity) {
//        Assert.notNull(entity, () -> new ServiceException("活动不存在"));
//        Assert.isTrue(entity.getStatus().equals(ActivityStatusEnum.RELEASE), () -> new ServiceException("活动未发布"));
//        switch (url) {
//            case "/a/":
//                // 需要报名
//                Assert.isTrue(entity.getApplyEnabled(), () -> new ServiceException("活动未开启报名"));
//                // 校验报名人数是否超过限制
//                if (entity.getApplyLimitEnabled()) {
//                    long applyCount = activityApplyMapper.selectCount(Wrappers.<ActivityApplyEntity>lambdaQuery()
//                        .eq(ActivityApplyEntity::getActivityId, entity.getId()));
//                    Assert.isTrue(applyCount < entity.getApplyLimit(), () -> new ServiceException("报名人数已满"));
//                }
//                break;
//            case "/f/":
//                // 需要填写表单
//                Assert.isTrue(entity.getFormEnabled(), () -> new ServiceException("活动未开启表单"));
//                break;
//            case "/s/":
//                // 需要签到
//                Assert.isTrue(entity.getSigninEnabled(), () -> new ServiceException("活动未开启签到"));
//                // 校验签到时间是否在活动时间内
//                boolean signinValid = DateUtil.date().isAfter(entity.getSigninStartTime()) && DateUtil.date().isBefore(entity.getSigninEndTime());
//                Assert.isTrue(signinValid, () -> new ServiceException("不在签到时间范围内"));
//                break;
//            default:
//                throw new ServiceException("url错误");
//        }
//    }

    @Override
    public ActivityVO getById(Long id) {
        String activityRoleKey = getActivityRoleKey(id);
        if (StringUtils.isNotBlank(activityRoleKey)) {
            ActivityEntity entity = activityMapper.selectOne(Wrappers.<ActivityEntity>lambdaQuery()
                .eq(ActivityEntity::getId, id));
            ActivityVO vo = BeanUtil.copyProperties(entity, ActivityVO.class);
            vo.setRoleKey(activityRoleKey);
            // 查询项目管理员
            List<SysUserVo> managerUsers = getUsersByActivityAndRole(id, ROLE_KEY_ACTIVITY_MANAGER);
            if (CollUtil.isNotEmpty(managerUsers)) {
                vo.setActivityManagerUserIds(managerUsers.stream()
                    .map(SysUserVo::getUserId)
                    .toArray(Long[]::new));
            }
            // 查询项目成员
            List<SysUserVo> memberUsers = getUsersByActivityAndRole(id, ROLE_KEY_ACTIVITY_MEMBER);
            if (CollUtil.isNotEmpty(memberUsers)) {
                vo.setActivityMemberUserIds(memberUsers.stream()
                    .map(SysUserVo::getUserId)
                    .toArray(Long[]::new));
            }
            // 查询项目关联部门
            List<SysDeptVo> memberDepts = getDeptsByProject(id);
            if (CollUtil.isNotEmpty(memberDepts)) {
                vo.setActivityDeptIds(memberDepts.stream()
                    .map(SysDeptVo::getDeptId)
                    .toArray(Long[]::new));
            }

            // 查询制定名单
            if (entity.getApplyLimitType().equals(FormCollectLimitTypeEnum.SPECIFIC)) {
                UserFormAuthEntity auth = userFormAuthMapper.selectOne(Wrappers.<UserFormAuthEntity>lambdaQuery()
                    .eq(UserFormAuthEntity::getFormKey, entity.getFormKey()));
                if (auth != null) {
                    vo.setUserIdList(auth.getUserIdList());
                    vo.setDeptIdList(auth.getDeptIdList());
                }
            }

            // 查询活动是否维护过组件
            long itemCount = userFormItemMapper.selectCount(Wrappers.<UserFormItemEntity>lambdaQuery()
                .eq(UserFormItemEntity::getFormKey, entity.getFormKey()));
            vo.setCreated(itemCount > 0);
            return vo;
        }
        return null;

    }

    /**
     * 根据活动ID和角色获取用户列表
     *
     * @param id      活动ID
     * @param roleKey 角色键
     * @return 用户列表
     */
    private List<SysUserVo> getUsersByActivityAndRole(Long id, String roleKey) {
        // 查询项目-角色-用户关联关系
        LambdaQueryWrapper<ActivityMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityMemberRole::getActivityId, id)
            .eq(ActivityMemberRole::getRoleKey, roleKey)
            .isNotNull(ActivityMemberRole::getUserId);

        List<ActivityMemberRole> memberRoles = activityMemberRoleMapper.selectList(queryWrapper);

        // 提取用户ID并查询用户信息
        List<Long> userIds = memberRoles.stream()
            .map(ActivityMemberRole::getUserId)
            .distinct()
            .toList();

        if (userIds.isEmpty()) {
            return List.of();
        }

        // 调用系统用户服务获取用户信息
        return sysUserService.selectUserByIds(userIds, null);
    }

    /**
     * 获取活动关联的部门列表
     *
     * @param id 活动ID
     * @return 部门列表
     */
    private List<SysDeptVo> getDeptsByProject(Long id) {
        // 查询项目-角色-部门关联关系
        LambdaQueryWrapper<ActivityMemberRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityMemberRole::getActivityId, id)
            .isNotNull(ActivityMemberRole::getDeptId);

        List<ActivityMemberRole> memberRoles = activityMemberRoleMapper.selectList(queryWrapper);

        // 提取部门ID并查询部门信息
        List<Long> deptIds = memberRoles.stream()
            .map(ActivityMemberRole::getDeptId)
            .distinct()
            .toList();

        if (deptIds.isEmpty()) {
            return List.of();
        }

        // 调用系统部门服务获取部门信息
        return sysDeptService.selectDeptByIds(deptIds);
    }


    /***
     * 根据key查询活动信息
     * @param key     活动key
     * @return 活动信息
     */
    @Override
    public ActivityApplyDetailVO getApplyDetail(String key) {
        ActivityApplyDetailVO detailVO = new ActivityApplyDetailVO();
        ActivityEntity entity = getByKey(key);
        BeanUtil.copyProperties(entity, detailVO);
        getLoginUserApplyState(entity, detailVO);
        return detailVO;
    }

    /***
     * 根据key查询签到信息
     * @param key     活动key
     * @return 活动信息
     */
    @Override
    public ActivitySigninDetailVO getSigninDetail(String key) {
        ActivitySigninDetailVO detailVO = new ActivitySigninDetailVO();
        ActivityEntity entity = getByKey(key);
        BeanUtil.copyProperties(entity, detailVO);
        getActivitySigninStatus(entity, detailVO);
        return detailVO;
    }

    private void getActivitySigninStatus(ActivityEntity entity, ActivitySigninDetailVO detailVO) {
        if (entity.getSigninEnabled()) {
            ActivitySigninEntity signined = activitySigninMapper.selectOne(Wrappers.<ActivitySigninEntity>lambdaQuery()
                .eq(ActivitySigninEntity::getActivityId, entity.getId())
                .eq(ActivitySigninEntity::getUserId, LoginHelper.getUserId())
                .last("limit 1"));
            if (signined == null) {
                detailVO.setSignStatus(ActivitySignStatusEnum.SIGN);
            } else {
                detailVO.setSignStatus(ActivitySignStatusEnum.SIGNED);
                detailVO.setSigninedTime(signined.getCreateTime());
            }
        } else {
            detailVO.setSignStatus(ActivitySignStatusEnum.CLOSE);
        }

        // 校验是否开启了签到
//        boolean signinValid = DateUtil.date().isAfter(entity.getSigninStartTime()) && DateUtil.date().isBefore(entity.getSigninEndTime());
//        if (entity.getSigninEnabled() || signinValid) {
//            detailVO.setSignStatus(ActivitySignStatusEnum.SIGN);
//            detailVO.setSigninStartTime(entity.getSigninStartTime());
//            detailVO.setSigninEndTime(entity.getSigninEndTime());
//            return;
//        } else {
//            detailVO.setSignStatus(ActivitySignStatusEnum.CLOSE);
//        }
//        if (entity.getApplySigninEnabled()) {
//            // 报名是否成功
//            long count = activityApplyMapper.selectCount(Wrappers.<ActivityApplyEntity>lambdaQuery()
//                .eq(ActivityApplyEntity::getActivityId, entity.getId())
//                .eq(ActivityApplyEntity::getUserId, LoginHelper.getUserId())
//                .eq(ActivityApplyEntity::getAuditState, ActivityApplyAuditStateEnum.PASS));
//            if (count <= 0) {
//                detailVO.setSignStatus(ActivitySignStatusEnum.NEED_APPLY);
//            }
//        } else {
//            // 是否已签到
//            ActivitySigninEntity signined = activitySigninMapper.selectOne(Wrappers.<ActivitySigninEntity>lambdaQuery()
//                .eq(ActivitySigninEntity::getActivityId, entity.getId())
//                .eq(ActivitySigninEntity::getUserId, LoginHelper.getUserId())
//                .last("limit 1"));
//            if (signined != null) {
//                detailVO.setSignStatus(ActivitySignStatusEnum.SIGNED);
//                detailVO.setSigninedTime(signined.getCreateTime());
//            }
//        }
    }

    /***
     * 根据id删除活动信息
     * @param id     活动id
     */
    @Override
    public Boolean deleteActivity(Long id) {
        ActivityEntity entity = activityMapper.selectById(id);
        if (entity.getStatus().equals(ActivityStatusEnum.RELEASE) || entity.getStatus().equals(ActivityStatusEnum.END)) {
            throw new ServiceException(MessageUtils.message("activity.status.abnormal.delete"));
        }
        // 查询登录人是否有删除权限
        String roleKey = getActivityRoleKey(id);
        if (roleKey.equals(ROLE_KEY_ACTIVITY_MEMBER)) {
            throw new ServiceException(MessageUtils.message("activity.no.permission.delete"));
        }
        return activityMapper.deleteById(id) > 0;
    }

    /***
     * 活动发布
     * @param id     活动id
     */
    @Override
    public Boolean publishForm(Long id) {
        ActivityEntity entity = activityMapper.selectById(id);
        Assert.isTrue(entity.getStatus().equals(ActivityStatusEnum.CREATE), () -> new ServiceException(MessageUtils.message("activity.status.abnormal.publish")));
        entity.setStatus(ActivityStatusEnum.RELEASE);
        return activityMapper.updateById(entity) > 0;
    }

    /***
     * 停止活动
     * @param id
     * @return
     */
    @Override
    public Boolean stopForm(Long id) {
        ActivityEntity entity = activityMapper.selectById(id);
        Assert.isTrue(entity.getStatus().equals(ActivityStatusEnum.RELEASE), () -> new ServiceException(MessageUtils.message("activity.status.abnormal.publish")));
        entity.setStatus(ActivityStatusEnum.END);
        return activityMapper.updateById(entity) > 0;
    }
}
