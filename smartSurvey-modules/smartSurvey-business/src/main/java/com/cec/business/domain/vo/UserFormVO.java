package com.cec.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cec.business.domain.bo.UserFormEntity;
import com.cec.business.domain.enums.FormStatusEnum;
import com.cec.business.domain.enums.FormTypeEnum;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.apache.ibatis.type.BooleanTypeHandler;

import java.io.Serializable;
import java.util.Date;

@AutoMapper(target = UserFormEntity.class)
@Data
public class UserFormVO implements Serializable {

    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 表单code
     */
    private String formKey;
    /**
     * 表单名称
     */
    private String name;

    /***
     * 状态
     */
    private FormStatusEnum status;

    /**
     * 表单类型
     */
    private FormTypeEnum type;

    /***
     * 创建人姓名
     */
    private String createByName;

    /***
     * 修改人姓名
     */
    private String updateByName;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新者
     */
    private Long updateBy;

    /***
     * 创建时间
     */
    private Date createTime;

    /***
     * 修改时间
     */
    private Date updateTime;

    /***
     * 答卷数量
     */
    private Long answeredCount = 0L;

    /**
     * 答题是否有有效期(1-是；0-否)
     */
    private Boolean validateEnabled;

    /**
     * 答题开始时间
     */
    private Date validateStartDate;

    /**
     * 答题结束时间
     */
    private Date validateEndDate;

}
