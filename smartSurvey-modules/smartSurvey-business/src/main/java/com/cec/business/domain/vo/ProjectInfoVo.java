package com.cec.business.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.cec.business.domain.bo.ProjectInfo;
import com.cec.system.domain.vo.SysDeptVo;
import com.cec.system.domain.vo.SysUserVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 项目信息视图对象 project_info
 *
 * <AUTHOR> Li
 * @date 2025-07-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProjectInfo.class)
public class ProjectInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long projectId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目编码
     */
    @ExcelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 任务描述
     */
    @ExcelProperty(value = "任务描述")
    private String content;

    /**
     * 创建用户id
     */
    @ExcelProperty(value = "创建用户id")
    private Long createUserId;

    /**
     * 创建用户账号
     */
    @ExcelProperty(value = "创建用户账号")
    private String createStaffNum;

    /**
     * 创建用户名称
     */
    @ExcelProperty(value = "创建用户名称")
    private String createStaffName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    private List<SysUserVo> projectManagerUsers;

    private List<SysUserVo> projectMemberUsers;

    private List<SysDeptVo> projectMemberDepts;

    private String roleKey;

}
