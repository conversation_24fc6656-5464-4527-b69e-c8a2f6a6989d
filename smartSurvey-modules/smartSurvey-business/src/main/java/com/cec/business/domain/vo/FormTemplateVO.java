package com.cec.business.domain.vo;

import com.cec.business.domain.bo.FormTemplateEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.util.Date;


@Data
@AutoMapper(target = FormTemplateEntity.class)
public class FormTemplateVO {

    private Long id;

    /**
     * 模板唯一标识
     */
    private String formKey;

    /***
     * 来源模版Id
     */
    private String sourceFormKey;

    /**
     * i1b
     * 表单名称
     */
    private String name;

    /**
     * 表单描述
     */
    private String description;

    /**
     * 创建者
     */
    private Long createBy;

    /***
     * 创建时间
     */
    private Date createTime;

    /***
     * 创建人登录名
     */
    private String createByName;

}
