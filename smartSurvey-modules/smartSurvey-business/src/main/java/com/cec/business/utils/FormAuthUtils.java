package com.cec.business.utils;

import cn.hutool.core.util.ObjectUtil;
import com.cec.business.domain.bo.UserFormEntity;
import com.cec.business.service.UserFormService;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.satoken.utils.LoginHelper;
import lombok.experimental.UtilityClass;

/***
 * 校验用户是否有权限操作问卷
 */
@UtilityClass
public class FormAuthUtils {

    /**
     * 是否拥有表单的权限
     */
    public void hasPermission(String formKey) {
        // 是否是超级管理员
        if (LoginHelper.isSuperAdmin(LoginHelper.getUserId())) {
            return;
        }
        UserFormService userFormService = SpringUtils.getBean(UserFormService.class);
        UserFormEntity userFormEntity = userFormService.getByKey(formKey);
        if (ObjectUtil.isNull(userFormEntity)) {
            return;
        }
        if (!userFormEntity.getCreateBy().equals(LoginHelper.getUserId())) {
            throw new ServiceException(MessageUtils.message("form.no.permission"));
        }
    }
}
